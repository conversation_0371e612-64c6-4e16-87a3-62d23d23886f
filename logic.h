#ifndef LOGIC_H
#define LOGIC_H

#include "types.h"

extern const int dirRows[4];
extern const int dirCol[4];


// functions
void load_seed();
void apply_cell_effect(Player *p, const Cell *cell);
void setup_players(GameState *Game);
bool same_pos(Position a, Position b);
void place_flag_randomly(GameState *Game);
void blocked_cost(GameState *Game, Player *P);
bool attempt_move(GameState *Game, Player *P, int steps, bool *sent_to_bawana, bool *capture, int *cells_moved, long long *cost, bool *wall_block);
void check_capture(GameState *Game, Player* P, bool *captured);
Cell *check_pole(GameState *Game, Cell *current);
Cell *check_stairs(GameState *Game, Cell *current);
bool can_use_stair(Stair *stair, Position current_pos);
int man_best_distance(Position pos1, Position pos2);
void initialize_poles(GameState *Game);
void initialize_stairs(GameState *Game);
void randomize_stairs_direction(GameState *Game);
void apply_bawana(GameState *Game, Player *p);
void exit_bawana(Player *p);
Cell *random_bawana_cell(GameState *Game);
void initialize_bawana(GameState *Game);

void consumables_randomize(GameState *Game);
void add_wall(GameState *Game, int floor, int row1,  int col1, int row2, int col2);
int min(int a, int b);
int max(int a, int b);
void init_cells(GameState *Game);
void init_walls(GameState *Game);
void set_cell (Cell *cell, CellKind kind, int consumables, int add, int mul);
bool is_in_board (int row, int col);
bool is_cell_available(GameState *Game, int floor, int row, int col);
bool is_cell_available_for_structures(GameState *Game, int floor, int row, int col);
Cell* get_cell (GameState *Game, int floor, int row, int col);
int set_direction(int die);
int rand_int(int low, int high);
bool is_valid_direction(int d);
char* direction_name(Direction dir);
void print_stair_dir(GameState *Game);
void print_debug_out(GameState *Game);
void load_stairs(GameState *Game);
void load_poles(GameState *Game);
void load_walls(GameState *Game);
void load_flag(GameState *Game);
int get_cell_number(Position pos);
void block_stair_passing_cells(GameState *Game, Stair *stair);
bool next_cell_check(GameState *Game, Position next, Player *P);
void apply_pole_or_stair(GameState *Game, Position *current, Player *P);
void initialize_game(GameState *Game);
void handle_stairs_direction(GameState *Game);
bool handle_food_poison(GameState *Game, Player *P, char *message);
bool handle_in_maze(GameState *Game, Player *P, int move_die);
void handle_dices(GameState *Game, Player *P, int move_die, char *message);
void move_player(GameState *Game, Player *P, int steps, char *message);
bool check_win(GameState *Game, Player *P);
void play(GameState *Game);
void log_message(char *format, ...);
void error_log(char *format, ...);
void init_logs();


#endif